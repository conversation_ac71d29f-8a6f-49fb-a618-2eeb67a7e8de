#!/usr/bin/env python3
"""
Sistema di Amministrazione SNIP
Gestione utenti, configurazioni, audit log e statistiche di sistema
"""

import json
import logging
from datetime import datetime, date, timedelta, timezone
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from fastapi import HTTPException
from models import (
    Agente,
    SystemConfig, AuditLog, UserSession, SystemStats,
    Viaggio, Navi, Armatore
)
from passlib.context import CryptContext

# Setup logging
logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AdminManager:
    """Gestore principale per le funzionalità di amministrazione"""

    def __init__(self) -> None:
        """Inizializza AdminManager - non eredita da altre classi"""
        self.logger = logger
    
    # ===== GESTIONE UTENTI =====
    
    def get_all_users(self, db: Session, skip: int = 0, limit: int = 100) -> List[Agente]:
        """Ottiene tutti gli utenti con paginazione"""
        return db.query(Agente).offset(skip).limit(limit).all()
    
    def get_user_by_id(self, db: Session, user_id: int) -> Optional[Agente]:
        """Ottiene un utente per ID"""
        return db.query(Agente).filter(Agente.id_user == user_id).first()
    
    def create_user(self, db: Session, user_data: Dict[str, Any], created_by: int) -> Agente:
        """Crea un nuovo utente"""
        try:
            # Verifica che l'email non esista già
            existing_user = db.query(Agente).filter(Agente.email == user_data['email']).first()
            if existing_user:
                raise HTTPException(status_code=400, detail="Email già esistente")
            
            # Hash della password
            hashed_password: str = pwd_context.hash(user_data['password'])  # type: ignore
            
            # Crea nuovo utente
            new_user = Agente(
                Nome=user_data['nome'],
                Cognome=user_data['cognome'],
                email=user_data['email'],
                password=hashed_password,
                reparto=user_data['reparto'],
                ruolo=user_data.get('ruolo', 'USER'),
                visibile=user_data.get('visibile', 'no')
            )
            
            db.add(new_user)
            db.commit()
            db.refresh(new_user)
            
            # Log dell'azione
            self.log_action(
                db=db,
                user_id=created_by,
                action="CREATE_USER",
                table_name="AGENTE",
                record_id=new_user.id_user,  # type: ignore
                new_values=json.dumps({
                    "nome": new_user.Nome,
                    "cognome": new_user.Cognome,
                    "email": new_user.email,
                    "reparto": new_user.reparto.value if hasattr(new_user.reparto, 'value') else str(new_user.reparto),
                    "ruolo": new_user.ruolo.value if hasattr(new_user.ruolo, 'value') else str(new_user.ruolo),
                    "visibile": new_user.visibile
                })
            )
            
            self.logger.info(f"Nuovo utente creato: {new_user.email} da utente {created_by}")
            return new_user
            
        except Exception as e:
            db.rollback()
            self.logger.error(f"Errore creazione utente: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Errore creazione utente: {str(e)}")
    
    def update_user(self, db: Session, user_id: int, user_data: Dict[str, Any], updated_by: int) -> Agente:
        """Aggiorna un utente esistente"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                raise HTTPException(status_code=404, detail="Utente non trovato")
            
            # Salva valori precedenti per audit
            old_values: Dict[str, Any] = {
                "nome": user.Nome,
                "cognome": user.Cognome,
                "email": user.email,
                "reparto": user.reparto.value if hasattr(user.reparto, 'value') else str(user.reparto),
                "ruolo": user.ruolo.value if hasattr(user.ruolo, 'value') else str(user.ruolo),
                "visibile": user.visibile
            }
            
            # Aggiorna campi
            if 'nome' in user_data:
                user.Nome = user_data['nome']
            if 'cognome' in user_data:
                user.Cognome = user_data['cognome']
            if 'email' in user_data:
                # Verifica unicità email
                existing = db.query(Agente).filter(
                    and_(Agente.email == user_data['email'], Agente.id_user != user_id)
                ).first()
                if existing:
                    raise HTTPException(status_code=400, detail="Email già esistente")
                user.email = user_data['email']
            if 'reparto' in user_data:
                user.reparto = user_data['reparto']  # type: ignore
            if 'ruolo' in user_data:
                user.ruolo = user_data['ruolo']  # type: ignore
            if 'visibile' in user_data:
                user.visibile = user_data['visibile']
            if 'password' in user_data and user_data['password']:
                user.password = pwd_context.hash(user_data['password'])  # type: ignore
            
            db.commit()
            db.refresh(user)
            
            # Nuovi valori per audit
            new_values: Dict[str, Any] = {
                "nome": user.Nome,
                "cognome": user.Cognome,
                "email": user.email,
                "reparto": user.reparto.value if hasattr(user.reparto, 'value') else str(user.reparto),
                "ruolo": user.ruolo.value if hasattr(user.ruolo, 'value') else str(user.ruolo),
                "visibile": user.visibile
            }
            
            # Log dell'azione
            self.log_action(
                db=db,
                user_id=updated_by,
                action="UPDATE_USER",
                table_name="AGENTE",
                record_id=user.id_user,  # type: ignore
                old_values=json.dumps(old_values),
                new_values=json.dumps(new_values)
            )
            
            self.logger.info(f"Utente aggiornato: {user.email} da utente {updated_by}")
            return user
            
        except Exception as e:
            db.rollback()
            self.logger.error(f"Errore aggiornamento utente: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Errore aggiornamento utente: {str(e)}")
    
    def delete_user(self, db: Session, user_id: int, deleted_by: int) -> bool:
        """Elimina un utente (soft delete impostando visibile='no')"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                raise HTTPException(status_code=404, detail="Utente non trovato")
            
            # Soft delete
            old_values = {"visibile": user.visibile}
            user.visibile = 'no'  # type: ignore
            
            db.commit()
            
            # Log dell'azione
            self.log_action(
                db=db,
                user_id=deleted_by,
                action="DELETE_USER",
                table_name="AGENTE",
                record_id=user.id_user,  # type: ignore
                old_values=json.dumps(old_values),
                new_values=json.dumps({"visibile": "no"})
            )
            
            self.logger.info(f"Utente eliminato: {user.email} da utente {deleted_by}")
            return True
            
        except Exception as e:
            db.rollback()
            self.logger.error(f"Errore eliminazione utente: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Errore eliminazione utente: {str(e)}")
    
    def search_users(self, db: Session, query: str, skip: int = 0, limit: int = 100) -> List[Agente]:
        """Cerca utenti per nome, cognome o email"""
        search_filter = or_(
            Agente.Nome.ilike(f"%{query}%"),
            Agente.Cognome.ilike(f"%{query}%"),
            Agente.email.ilike(f"%{query}%")
        )
        return db.query(Agente).filter(search_filter).offset(skip).limit(limit).all()
    
    # ===== CONFIGURAZIONI SISTEMA =====
    
    def get_config(self, db: Session, key: str) -> Optional[SystemConfig]:
        """Ottiene una configurazione per chiave"""
        return db.query(SystemConfig).filter(
            and_(SystemConfig.config_key == key, SystemConfig.is_active == True)
        ).first()
    
    def get_all_configs(self, db: Session) -> List[SystemConfig]:
        """Ottiene tutte le configurazioni attive"""
        return db.query(SystemConfig).filter(SystemConfig.is_active == True).all()
    
    def set_config(self, db: Session, key: str, value: str, description: Optional[str] = None,
                   config_type: str = 'string', user_id: Optional[int] = None) -> SystemConfig:
        """Imposta o aggiorna una configurazione"""
        try:
            config = self.get_config(db, key)
            
            if config:
                # Aggiorna esistente
                old_value = config.config_value
                config.config_value = value  # type: ignore
                config.description = description or config.description  # type: ignore
                config.config_type = config_type  # type: ignore
                config.updated_at = datetime.now(timezone.utc)  # type: ignore
                action = "UPDATE_CONFIG"
                old_values = {"value": old_value}
            else:
                # Crea nuovo
                config = SystemConfig(
                    config_key=key,
                    config_value=value,
                    description=description,
                    config_type=config_type
                )
                db.add(config)
                action = "CREATE_CONFIG"
                old_values = None
            
            db.commit()
            db.refresh(config)
            
            # Log dell'azione
            if user_id:
                self.log_action(
                    db=db,
                    user_id=user_id,
                    action=action,
                    table_name="SYSTEM_CONFIG",
                    record_id=config.id,  # type: ignore
                    old_values=json.dumps(old_values) if old_values else None,
                    new_values=json.dumps({"key": key, "value": value, "type": config_type})
                )
            
            self.logger.info(f"Configurazione {action.lower()}: {key} = {value}")
            return config
            
        except Exception as e:
            db.rollback()
            self.logger.error(f"Errore configurazione: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Errore configurazione: {str(e)}")
    
    # ===== AUDIT LOG =====
    
    def log_action(self, db: Session, user_id: Optional[int], action: str,
                   table_name: Optional[str] = None, record_id: Optional[int] = None,
                   old_values: Optional[str] = None, new_values: Optional[str] = None,
                   ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> None:
        """Registra un'azione nel log di audit"""
        try:
            audit_entry = AuditLog(
                user_id=user_id,
                action=action,
                table_name=table_name,
                record_id=record_id,
                old_values=old_values,
                new_values=new_values,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.add(audit_entry)
            db.commit()
            
        except Exception as e:
            self.logger.error(f"Errore logging audit: {str(e)}")
            # Non fare rollback per non interferire con l'operazione principale
    
    def get_audit_logs(self, db: Session, skip: int = 0, limit: int = 100,
                       user_id: Optional[int] = None, action: Optional[str] = None,
                       start_date: Optional[date] = None, end_date: Optional[date] = None) -> List[AuditLog]:
        """Ottiene i log di audit con filtri"""
        query = db.query(AuditLog)
        
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        if action:
            query = query.filter(AuditLog.action == action)
        if start_date:
            query = query.filter(AuditLog.timestamp >= start_date)
        if end_date:
            query = query.filter(AuditLog.timestamp <= end_date)
        
        return query.order_by(desc(AuditLog.timestamp)).offset(skip).limit(limit).all()

    # ===== STATISTICHE SISTEMA =====

    def get_system_stats(self, db: Session, stat_date: Optional[date] = None) -> Optional[SystemStats]:
        """Ottiene le statistiche per una data specifica"""
        if stat_date is None:
            stat_date = date.today()

        return db.query(SystemStats).filter(SystemStats.stat_date == stat_date).first()

    def update_daily_stats(self, db: Session, stat_date: Optional[date] = None) -> SystemStats:
        """Aggiorna le statistiche giornaliere"""
        if stat_date is None:
            stat_date = date.today()

        try:
            # Calcola statistiche
            total_users = db.query(func.count(Agente.id_user)).filter(Agente.visibile == 'si').scalar()
            active_users = db.query(func.count(UserSession.id)).filter(
                and_(UserSession.is_active == True, UserSession.last_activity >= datetime.now() - timedelta(hours=24))
            ).scalar()
            total_viaggi = db.query(func.count(Viaggio.id)).scalar()

            # Cerca statistiche esistenti per oggi
            stats = self.get_system_stats(db, stat_date)

            if stats:
                # Aggiorna esistenti
                stats.total_users = total_users
                stats.active_users = active_users
                stats.total_viaggi = total_viaggi
            else:
                # Crea nuove
                stats = SystemStats(
                    stat_date=stat_date,
                    total_users=total_users,
                    active_users=active_users,
                    total_viaggi=total_viaggi
                )
                db.add(stats)

            db.commit()
            db.refresh(stats)

            self.logger.info(f"Statistiche aggiornate per {stat_date}")
            return stats

        except Exception as e:
            db.rollback()
            self.logger.error(f"Errore aggiornamento statistiche: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Errore statistiche: {str(e)}")

    def get_stats_range(self, db: Session, start_date: date, end_date: date) -> List[SystemStats]:
        """Ottiene statistiche per un range di date"""
        return db.query(SystemStats).filter(
            and_(SystemStats.stat_date >= start_date, SystemStats.stat_date <= end_date)
        ).order_by(SystemStats.stat_date).all()

    def get_dashboard_summary(self, db: Session) -> Dict[str, Any]:
        """Ottiene un riepilogo per la dashboard admin"""
        try:
            # Statistiche base
            total_users = db.query(func.count(Agente.id_user)).filter(Agente.visibile == 'si').scalar()
            total_viaggi = db.query(func.count(Viaggio.id)).scalar()
            total_navi = db.query(func.count(Navi.id)).scalar()
            total_armatori = db.query(func.count(Armatore.id)).scalar()

            # Sessioni attive (ultime 24 ore)
            active_sessions = db.query(func.count(UserSession.id)).filter(
                and_(
                    UserSession.is_active == True,
                    UserSession.last_activity >= datetime.now() - timedelta(hours=24)
                )
            ).scalar()

            # Utenti per reparto
            users_by_reparto = db.query(
                Agente.reparto, func.count(Agente.id_user)
            ).filter(Agente.visibile == 'si').group_by(Agente.reparto).all()

            # Utenti per ruolo
            users_by_ruolo = db.query(
                Agente.ruolo, func.count(Agente.id_user)
            ).filter(Agente.visibile == 'si').group_by(Agente.ruolo).all()

            # Attività recenti (ultimi 7 giorni)
            recent_activity = db.query(func.count(AuditLog.id)).filter(
                AuditLog.timestamp >= datetime.now() - timedelta(days=7)
            ).scalar()

            # Viaggi recenti (ultimo mese) - usa data_arrivo
            recent_viaggi = db.query(func.count(Viaggio.id)).filter(
                Viaggio.data_arrivo >= datetime.now().date() - timedelta(days=30)
            ).scalar()

            # Converti enum in stringhe per JSON
            reparto_dict = {}
            for reparto, count in users_by_reparto:
                reparto_str = reparto.value if hasattr(reparto, 'value') else str(reparto)
                reparto_dict[reparto_str] = count

            ruolo_dict = {}
            for ruolo, count in users_by_ruolo:
                ruolo_str = ruolo.value if hasattr(ruolo, 'value') else str(ruolo)
                ruolo_dict[ruolo_str] = count

            return {
                "totals": {
                    "users": total_users,
                    "viaggi": total_viaggi,
                    "navi": total_navi,
                    "armatori": total_armatori,
                    "active_sessions": active_sessions
                },
                "users_by_reparto": reparto_dict,
                "users_by_ruolo": ruolo_dict,
                "activity": {
                    "recent_actions": recent_activity,
                    "recent_viaggi": recent_viaggi
                },
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Errore dashboard summary: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Errore dashboard: {str(e)}")

    # ===== GESTIONE SESSIONI =====

    def get_active_sessions(self, db: Session) -> List[UserSession]:
        """Ottiene tutte le sessioni attive"""
        return db.query(UserSession).filter(UserSession.is_active == True).all()

    def invalidate_session(self, db: Session, session_id: int, admin_user_id: int) -> bool:
        """Invalida una sessione specifica"""
        try:
            session = db.query(UserSession).filter(UserSession.id == session_id).first()
            if not session:
                return False

            session.is_active = False  # type: ignore
            db.commit()

            # Log dell'azione
            self.log_action(
                db=db,
                user_id=admin_user_id,
                action="INVALIDATE_SESSION",
                table_name="USER_SESSIONS",
                record_id=session_id,
                new_values=json.dumps({"session_invalidated": True})
            )

            self.logger.info(f"Sessione {session_id} invalidata da admin {admin_user_id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Errore invalidazione sessione: {str(e)}")
            return False

    def invalidate_user_sessions(self, db: Session, user_id: int, admin_user_id: int) -> int:
        """Invalida tutte le sessioni di un utente"""
        try:
            sessions = db.query(UserSession).filter(
                and_(UserSession.user_id == user_id, UserSession.is_active == True)
            ).all()

            count = 0
            for session in sessions:
                session.is_active = False  # type: ignore
                count += 1

            db.commit()

            # Log dell'azione
            self.log_action(
                db=db,
                user_id=admin_user_id,
                action="INVALIDATE_USER_SESSIONS",
                table_name="USER_SESSIONS",
                record_id=user_id,
                new_values=json.dumps({"sessions_invalidated": count})
            )

            self.logger.info(f"{count} sessioni invalidate per utente {user_id} da admin {admin_user_id}")
            return count

        except Exception as e:
            db.rollback()
            self.logger.error(f"Errore invalidazione sessioni utente: {str(e)}")
            return 0

    # ===== UTILITÀ =====

    def cleanup_old_data(self, db: Session, days_to_keep: int = 90) -> Dict[str, int]:
        """Pulisce dati vecchi dal sistema"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            # Pulisci audit log vecchi
            old_audits = db.query(AuditLog).filter(AuditLog.timestamp < cutoff_date).count()
            db.query(AuditLog).filter(AuditLog.timestamp < cutoff_date).delete()

            # Pulisci sessioni scadute
            old_sessions = db.query(UserSession).filter(
                or_(
                    UserSession.expires_at < datetime.now(),
                    and_(UserSession.is_active == False, UserSession.created_at < cutoff_date)
                )
            ).count()
            db.query(UserSession).filter(
                or_(
                    UserSession.expires_at < datetime.now(),
                    and_(UserSession.is_active == False, UserSession.created_at < cutoff_date)
                )
            ).delete()

            # Pulisci statistiche vecchie (mantieni solo 1 anno)
            old_stats_date = date.today() - timedelta(days=365)
            old_stats = db.query(SystemStats).filter(SystemStats.stat_date < old_stats_date).count()
            db.query(SystemStats).filter(SystemStats.stat_date < old_stats_date).delete()

            db.commit()

            result = {
                "audit_logs_deleted": old_audits,
                "sessions_deleted": old_sessions,
                "stats_deleted": old_stats
            }

            self.logger.info(f"Cleanup completato: {result}")
            return result

        except Exception as e:
            db.rollback()
            self.logger.error(f"Errore cleanup: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Errore cleanup: {str(e)}")

# Istanza globale del manager
admin_manager = AdminManager()
