{"include": ["."], "exclude": ["**/node_modules", "**/__pycache__", ".git", "**/*.pyc", ".venv/**", "venv/**", "env/**", ".env/**"], "pythonVersion": "3.13", "pythonPlatform": "Windows", "executionEnvironments": [{"root": ".", "pythonVersion": "3.13", "pythonPlatform": "Windows", "extraPaths": ["C:/Users/<USER>/AppData/Roaming/Python/Python313/site-packages"]}], "reportMissingImports": "warning", "reportMissingTypeStubs": false, "reportImportCycles": false, "reportUnusedImport": "none", "reportUnusedClass": "none", "reportUnusedFunction": "none", "reportUnusedVariable": "none", "reportDuplicateImport": "warning", "reportOptionalSubscript": "information", "reportOptionalMemberAccess": "information", "reportOptionalCall": "information", "reportOptionalIterable": "information", "reportOptionalContextManager": "information", "reportOptionalOperand": "information", "reportTypedDictNotRequiredAccess": "information", "reportPrivateImportUsage": "information", "reportConstantRedefinition": "warning", "reportIncompatibleMethodOverride": "warning", "reportIncompatibleVariableOverride": "warning", "reportInconsistentConstructor": "none", "reportOverlappingOverloads": "warning", "reportMissingSuperCall": "none", "reportUninitializedInstanceVariable": "information", "reportInvalidStringEscapeSequence": "warning", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownLambdaType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingTypeArgument": "information", "reportInvalidTypeVarUse": "warning", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "information", "reportUnnecessaryCast": "information", "reportUnnecessaryComparison": "information", "reportAssertAlwaysTrue": "warning", "reportSelfClsParameterName": "information", "reportImplicitStringConcatenation": "information", "reportUndefinedVariable": "error", "reportUnboundVariable": "error", "reportInvalidStubStatement": "error", "reportIncompleteStub": "error", "reportUnsupportedDunderAll": "error", "reportUnusedCoroutine": "error", "reportFunctionMemberAccess": "error", "reportUnnecessaryTypeIgnoreComment": "none", "reportImplicitOverride": "none", "reportShadowedImports": "none"}